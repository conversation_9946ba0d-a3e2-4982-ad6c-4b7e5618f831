@echo off
echo ========================================
echo Steam Manifest Updater - Tool Launcher
echo ========================================
echo.
echo Available tools:
echo 1. Main Client Tool (CLI)
echo 2. GUI Client Tool (Original)
echo 3. GUI Client Tool (Refactored - AI-Friendly)
echo 4. Mini Client Tool (Background)
echo 5. Manifest Generator
echo 6. Server Automation
echo 7. Test Tool (OCR)
echo 8. Encryption Tool
echo 9. Build Tools (PyInstaller)
echo 10. Exit
echo.
set /p choice="Select a tool (1-10): "

if "%choice%"=="1" (
    echo Starting Main Client Tool...
    cd src
    python client_tool.py --auth
    cd ..
) else if "%choice%"=="2" (
    echo Starting GUI Client Tool (Original)...
    cd src
    python client_tool_gui.py
    cd ..
) else if "%choice%"=="3" (
    echo Starting GUI Client Tool (Refactored - AI-Friendly)...
    cd src
    python client_tool_gui_refactored.py
    cd ..
) else if "%choice%"=="4" (
    echo Starting Mini Client Tool...
    cd src
    python mini_client_tool.py
    cd ..
) else if "%choice%"=="5" (
    echo Starting Manifest Generator...
    cd src
    python manifest_generator.py
    cd ..
) else if "%choice%"=="6" (
    echo Starting Server Automation...
    cd src
    python server_automation.py
    cd ..
) else if "%choice%"=="7" (
    echo Starting Test Tool...
    cd src
    python test.py
    cd ..
) else if "%choice%"=="8" (
    echo Starting Encryption Tool...
    cd src
    python encryption_tool.py
    cd ..
) else if "%choice%"=="9" (
    echo Building executables...
    pyinstaller config\MTYB_SteamTool.spec
    pyinstaller config\MTYB_MiniSteamTool.spec
    echo Build complete! Check dist\ folder for executables.
) else if "%choice%"=="10" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Please try again.
    pause
    goto :eof
)

pause
