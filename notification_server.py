#!/usr/bin/env python3
"""
AI Task Notification Script
A simple SMTP client for AI to send task completion notifications via email.
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime
import sys
import argparse

# Email configuration
EMAIL_CONFIG = {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "email": "<EMAIL>",
    "app_password": "nnqydxjgqxrdzsxg",
    "recipient": "<EMAIL>"
}

def send_email_notification(subject, content, content_type="plain"):
    """
    Send email notification using SMTP.

    Args:
        subject (str): Email subject
        content (str): Email content/body
        content_type (str): "plain" or "html"

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Add timestamp to subject
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        full_subject = f"🤖 AI Task: {subject}"

        # Create professional email template
        if content_type == "html":
            formatted_content = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }}
                    .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                    .content {{ background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }}
                    .timestamp {{ color: #6c757d; font-size: 0.9em; margin-top: 15px; }}
                    .footer {{ text-align: center; color: #6c757d; font-size: 0.8em; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; }}
                    h1 {{ margin: 0; font-size: 1.5em; }}
                    .status {{ font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🤖 AI Task Notification</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Steam Manifest Updater Project</p>
                </div>
                <div class="content">
                    {content}
                </div>
                <div class="timestamp">
                    📅 Completed: {timestamp}
                </div>
                <div class="footer">
                    <p>This notification was automatically generated by Claude AI</p>
                    <p>Steam Manifest Updater Tool | Task Completion System</p>
                </div>
            </body>
            </html>
            """
        else:
            formatted_content = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🤖 AI TASK NOTIFICATION                   ║
║                  Steam Manifest Updater Project             ║
╚══════════════════════════════════════════════════════════════╝

{content}

──────────────────────────────────────────────────────────────
📅 Completed: {timestamp}
🤖 Generated by: Claude AI
🔧 Project: Steam Manifest Updater Tool
──────────────────────────────────────────────────────────────

This is an automated notification from your AI assistant.
For support or questions, please check the project documentation.
            """

        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG["email"]
        msg['To'] = EMAIL_CONFIG["recipient"]
        msg['Subject'] = full_subject

        # Add body to email
        msg.attach(MIMEText(formatted_content.strip(), content_type))

        # Create SMTP session
        server = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        server.starttls()  # Enable security
        server.login(EMAIL_CONFIG["email"], EMAIL_CONFIG["app_password"])

        # Send email
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG["email"], EMAIL_CONFIG["recipient"], text)
        server.quit()

        print(f"✅ Email sent successfully!")
        print(f"📧 Subject: {full_subject}")
        print(f"📅 Time: {timestamp}")
        return True

    except Exception as e:
        print(f"❌ Failed to send email: {str(e)}")
        return False

def main():
    """Main function to handle command line arguments and send notifications."""
    parser = argparse.ArgumentParser(description='Send email notifications for AI task completion')
    parser.add_argument('subject', help='Email subject')
    parser.add_argument('content', help='Email content/message')
    parser.add_argument('--html', action='store_true', help='Send as HTML email (default: plain text)')
    parser.add_argument('--test', action='store_true', help='Send a test email')

    args = parser.parse_args()

    if args.test:
        # Send test email
        test_subject = "Test Notification"
        test_content = f"""
🧪 **TEST NOTIFICATION**

This is a test notification from your AI Task Notification Script.

**Test Details:**
• Status: ✅ Script is working correctly
• Timestamp: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
• Recipient: {EMAIL_CONFIG['recipient']}
• Email Format: Professional template active

**Next Steps:**
✓ You can now use this script to receive notifications when AI tasks are completed!
✓ The script supports both plain text and HTML email formats
✓ All notifications include professional formatting and timestamps

**Usage Examples:**
• Basic: python3 notification_server.py "Task Done" "Description here"
• HTML: python3 notification_server.py "Task Done" "<b>HTML content</b>" --html
        """
        success = send_email_notification(test_subject, test_content.strip())
    else:
        # Send notification with provided subject and content
        content_type = "html" if args.html else "plain"
        success = send_email_notification(args.subject, args.content, content_type)

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 AI TASK NOTIFICATION SYSTEM")
    print("Steam Manifest Updater Project")
    print("=" * 60)
    print(f"📧 Email recipient: {EMAIL_CONFIG['recipient']}")
    print("=" * 60)

    # If no arguments provided, show usage
    if len(sys.argv) == 1:
        print("\n📋 USAGE GUIDE:")
        print("  python3 notification_server.py 'Subject' 'Content'")
        print("  python3 notification_server.py --test")
        print("  python3 notification_server.py 'Subject' 'Content' --html")
        print("\n✨ EXAMPLES:")
        print("  python3 notification_server.py 'Task Completed' 'Steam manifest update finished successfully!'")
        print("  python3 notification_server.py --test")
        print("  python3 notification_server.py 'HTML Test' '<h2>Task Done!</h2><p>All systems go!</p>' --html")
        print("\n🤖 FOR AI INTEGRATION:")
        print("  import subprocess")
        print("  subprocess.run(['python3', 'notification_server.py', 'AI Task Done', 'Your task completed!'])")
        print("\n💡 TIP: Use --html flag for rich formatting in email notifications")
        sys.exit(0)

    main()
