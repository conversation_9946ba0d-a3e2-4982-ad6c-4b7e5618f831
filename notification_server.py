#!/usr/bin/env python3
"""
AI Task Notification Script
A simple SMTP client for AI to send task completion notifications via email.
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime
import sys
import argparse

# Email configuration
EMAIL_CONFIG = {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "email": "<EMAIL>",
    "app_password": "nnqydxjgqxrdzsxg",
    "recipient": "<EMAIL>"
}

def send_email_notification(subject, content, content_type="plain"):
    """
    Send email notification using SMTP.

    Args:
        subject (str): Email subject
        content (str): Email content/body
        content_type (str): "plain" or "html"

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Add timestamp to subject
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        full_subject = f"[{timestamp}] {subject}"

        # Create message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG["email"]
        msg['To'] = EMAIL_CONFIG["recipient"]
        msg['Subject'] = full_subject

        # Add body to email
        msg.attach(MIMEText(content, content_type))

        # Create SMTP session
        server = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        server.starttls()  # Enable security
        server.login(EMAIL_CONFIG["email"], EMAIL_CONFIG["app_password"])

        # Send email
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG["email"], EMAIL_CONFIG["recipient"], text)
        server.quit()

        print(f"✓ Email sent successfully: {full_subject}")
        return True

    except Exception as e:
        print(f"✗ Failed to send email: {str(e)}")
        return False

def main():
    """Main function to handle command line arguments and send notifications."""
    parser = argparse.ArgumentParser(description='Send email notifications for AI task completion')
    parser.add_argument('subject', help='Email subject')
    parser.add_argument('content', help='Email content/message')
    parser.add_argument('--html', action='store_true', help='Send as HTML email (default: plain text)')
    parser.add_argument('--test', action='store_true', help='Send a test email')

    args = parser.parse_args()

    if args.test:
        # Send test email
        test_subject = "Test Notification from AI Script"
        test_content = f"""
This is a test notification from your AI Task Notification Script.

Details:
- Timestamp: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- Status: Script is working correctly
- Recipient: {EMAIL_CONFIG['recipient']}

You can now use this script to receive notifications when AI tasks are completed!
        """
        success = send_email_notification(test_subject, test_content.strip())
    else:
        # Send notification with provided subject and content
        content_type = "html" if args.html else "plain"
        success = send_email_notification(args.subject, args.content, content_type)

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    print("=" * 50)
    print("AI TASK NOTIFICATION SCRIPT")
    print("=" * 50)
    print(f"Email recipient: {EMAIL_CONFIG['recipient']}")
    print("=" * 50)

    # If no arguments provided, show usage
    if len(sys.argv) == 1:
        print("\nUsage:")
        print("  python notification_server.py 'Subject' 'Content'")
        print("  python notification_server.py --test")
        print("  python notification_server.py 'Subject' 'Content' --html")
        print("\nExamples:")
        print("  python notification_server.py 'Task Completed' 'Steam manifest update finished successfully!'")
        print("  python notification_server.py --test")
        print("  python notification_server.py 'HTML Test' '<h1>Task Done!</h1><p>All good!</p>' --html")
        print("\nFor AI to use:")
        print("  import subprocess")
        print("  subprocess.run(['python', 'notification_server.py', 'AI Task Done', 'Your task completed!'])")
        sys.exit(0)

    main()
